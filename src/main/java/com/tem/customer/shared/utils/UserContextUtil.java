package com.tem.customer.shared.utils;

import com.iplatform.common.transform.TransformUtils;
import com.iplatform.common.utils.LogUtils;
import com.tem.customer.shared.common.Constant;
import com.tem.platform.api.dto.UserDto;
import com.tem.platform.security.authorize.ContextUtil;
import com.tem.platform.security.authorize.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Optional;


/**
 * 兼容性用户上下文工具类
 * 整合BaseController中的用户信息相关代码，提供兼容两种登录方式的用户信息获取方法
 * <p>
 * 主要功能：
 * 1. 兼容两种登录方式：Cookie登录（ContextUtil）和Token登录（SaTokenUserContextUtil）
 * 2. 优先使用Cookie登录方式，失败时自动回退到Token登录方式
 * 3. 支持从Session和请求参数中获取partnerId
 * 4. 支持权限检查（如超级管理员检查）
 * 5. 支持响应对象转换
 * 6. 支持请求上下文操作
 * 7. 支持Model数据设置
 * 8. 提供完整的Sa-Token功能委托
 * <p>
 * 登录方式兼容策略：
 * - getCurrentUser()、getCurrentUserId()、getCurrentUserFullname()等核心方法
 *   优先尝试ContextUtil.getCurrentUser()（Cookie登录）
 * - 如果Cookie登录失败或返回null，自动回退到SaTokenUserContextUtil对应方法（Token登录）
 * - 异常处理：捕获ContextUtil异常并记录警告日志，然后回退到Token方式
 * - 保持向后兼容：原有的Legacy方法仍然保留
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
public class UserContextUtil {


    /**
     * 响应对象的ThreadLocal存储
     */
    private static final ThreadLocal<HttpServletResponse> responseLocal = new ThreadLocal<>();


    /**
     * 获取当前登录用户（兼容ContextUtil方式）
     * 优先使用ContextUtil.getCurrentUser()，如果获取不到则使用Sa-Token方式
     * 注意：如果ContextUtil和User类不存在，请注释掉此方法
     *
     * @return 当前用户对象，如果未登录则返回null
     */
    public static User getCurrentUserLegacy() {
        try {
            // 优先使用ContextUtil方式获取用户
            User user = ContextUtil.getCurrentUser();
            if (user != null) {
                return user;
            }

            // 如果ContextUtil获取不到，尝试从Sa-Token获取并转换
            UserDto userDto = SaTokenUserContextUtil.getCurrentUser();
            if (userDto != null) {
                User legacyUser = new User();
                TransformUtils.transform(userDto, legacyUser);
                return legacyUser;
            }

            return null;
        } catch (Exception e) {
            LogUtils.error(log, "获取当前用户信息异常", e);
            return null;
        }
    }


    /**
     * 获取当前用户的TMC ID（企业ID）
     *
     * @return TMC ID，如果未登录则返回null
     */
    public static Long getTmcId() {
        Long partnerId = ContextUtil.getCurrentUser().getPartnerId();
        if(null == partnerId){
            partnerId = SaTokenUserContextUtil.getCurrentUserPartnerId();
        }
        return partnerId;
    }


    /**
     * 获取当前登录用户对象（兼容两种登录方式）
     * 优先使用ContextUtil.getCurrentUser()，如果获取不到则使用Sa-Token方式
     *
     * @return 当前用户对象，如果未登录则返回null
     */
    public static UserDto getCurrentUser() {
        long threadId = Thread.currentThread().getId();
        String threadName = Thread.currentThread().getName();

        try {
            // 优先使用ContextUtil方式获取用户
            User user = ContextUtil.getCurrentUser();
            if (user != null) {
                // 将User对象转换为UserDto对象
                UserDto userDto = new UserDto();
                TransformUtils.transform(user, userDto);
                return userDto;
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUser();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取用户失败，回退到Sa-Token方式 - 线程ID: {}, 异常: {}",
                    threadId, e.getMessage(), e);
            // 异常时回退到Sa-Token方式
            UserDto fallbackUser = SaTokenUserContextUtil.getCurrentUser();
            if (fallbackUser != null) {
                LogUtils.info(log, "异常回退Sa-Token用户 - 线程ID: {}, 用户ID: {}, 用户名: {}",
                        threadId, fallbackUser.getId(), fallbackUser.getFullname());
            }
            return fallbackUser;
        }
    }

    /**
     * 获取当前用户ID（兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 当前用户ID，如果未登录则返回null
     */
    public static Long getCurrentUserId() {
        try {
            // 优先使用ContextUtil方式获取用户ID
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getId() != null) {
                return user.getId();
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserId();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取用户ID失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserId();
        }
    }

    /**
     * 获取当前登录用户ID（字符串形式，兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 用户ID字符串，未登录返回null
     */
    public static String getCurrentUserIdAsString() {
        try {
            // 优先使用ContextUtil方式获取用户ID
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getId() != null) {
                return user.getId().toString();
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserIdAsString();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取用户ID字符串失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserIdAsString();
        }
    }

    /**
     * 获取当前登录用户对象（Optional包装，兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return Optional包装的当前用户对象
     */
    public static Optional<UserDto> getCurrentUserOptional() {
        return Optional.ofNullable(getCurrentUser());
    }

    /**
     * 获取当前用户全名（兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 当前用户全名，如果未登录则返回null
     */
    public static String getCurrentUserFullname() {
        try {
            // 优先使用ContextUtil方式获取用户全名
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getFullname() != null) {
                return user.getFullname();
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserFullname();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取用户全名失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserFullname();
        }
    }

    /**
     * 获取当前用户归属的企业ID（兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 企业ID，如果未登录或未关联企业则返回null
     */
    public static Long getCurrentUserPartnerId() {
        try {
            // 优先使用ContextUtil方式获取企业ID
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getPartnerId() != null) {
                return user.getPartnerId();
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserPartnerId();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取企业ID失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserPartnerId();
        }
    }

    /**
     * 获取当前用户的组织ID（兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 组织ID，如果未登录或未关联组织则返回null
     */
    public static Long getCurrentUserOrgId() {
        try {
            // 优先使用ContextUtil方式获取组织ID
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getOrgId() != null) {
                return user.getOrgId();
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserOrgId();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取组织ID失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.getCurrentUserOrgId();
        }
    }

    /**
     * 获取用户上下文快照（兼容两种登录方式）
     * 优先使用ContextUtil方式，如果获取不到则使用Sa-Token方式
     *
     * @return 用户上下文快照，如果未登录则返回null
     */
    public static UserContextSnapshot getCurrentUserSnapshot() {
        try {
            // 优先使用ContextUtil方式获取用户快照
            User user = ContextUtil.getCurrentUser();
            if (user != null) {
                SaTokenUserContextUtil.UserContextSnapshot snapshot =
                    new SaTokenUserContextUtil.UserContextSnapshot(
                        user.getId(),
                        user.getUsername(),
                        user.getFullname(),
                        user.getPartnerId(),
                        user.getOrgId()
                    );
                return new UserContextSnapshot(snapshot);
            }

            // 如果ContextUtil获取不到，使用Sa-Token方式
            SaTokenUserContextUtil.UserContextSnapshot snapshot = SaTokenUserContextUtil.getCurrentUserSnapshot();
            return snapshot != null ? new UserContextSnapshot(snapshot) : null;

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式获取用户快照失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            SaTokenUserContextUtil.UserContextSnapshot snapshot = SaTokenUserContextUtil.getCurrentUserSnapshot();
            return snapshot != null ? new UserContextSnapshot(snapshot) : null;
        }
    }

    /**
     * 检查当前用户是否已登录（兼容两种登录方式）
     * 优先检查ContextUtil方式，如果未登录则检查Sa-Token方式
     *
     * @return true-已登录，false-未登录
     */
    public static boolean isLogin() {
        try {
            // 优先检查ContextUtil方式的登录状态
            User user = ContextUtil.getCurrentUser();
            if (user != null && user.getId() != null) {
                return true;
            }

            // 如果ContextUtil未登录，检查Sa-Token方式
            return SaTokenUserContextUtil.isLogin();

        } catch (Exception e) {
            LogUtils.warn(log, "ContextUtil方式检查登录状态失败，回退到Sa-Token方式", e);
            // 异常时回退到Sa-Token方式
            return SaTokenUserContextUtil.isLogin();
        }
    }

    /**
     * 获取当前用户的Token值
     * 委托给SaTokenUserContextUtil
     *
     * @return Token值，如果未登录则返回null
     */
    public static String getCurrentToken() {
        return SaTokenUserContextUtil.getCurrentToken();
    }

    /**
     * 获取Token剩余有效时间（秒）
     * 委托给SaTokenUserContextUtil
     *
     * @return 剩余有效时间，-1表示永不过期，-2表示已过期
     */
    public static long getTokenTimeout() {
        return SaTokenUserContextUtil.getTokenTimeout();
    }

    /**
     * 检查指定用户是否在线
     * 委托给SaTokenUserContextUtil
     *
     * @param userId 用户ID
     * @return 是否在线
     */
    public static boolean isLogin(Long userId) {
        return SaTokenUserContextUtil.isLogin(userId);
    }

    /**
     * 获取指定用户的Token列表
     * 委托给SaTokenUserContextUtil
     *
     * @param userId 用户ID
     * @return Token列表
     */
    public static List<String> getTokenValueListByLoginId(Long userId) {
        return SaTokenUserContextUtil.getTokenValueListByLoginId(userId);
    }

    /**
     * 获取当前会话的额外数据
     * 委托给SaTokenUserContextUtil
     *
     * @param key 数据键
     * @return 数据值
     */
    public static Object getExtra(String key) {
        return SaTokenUserContextUtil.getExtra(key);
    }

    /**
     * 设置当前会话的额外数据
     * 委托给SaTokenUserContextUtil
     *
     * @param key   数据键
     * @param value 数据值
     */
    public static void setExtra(String key, Object value) {
        SaTokenUserContextUtil.setExtra(key, value);
    }

    /**
     * 删除当前会话的额外数据
     * 委托给SaTokenUserContextUtil
     *
     * @param key 数据键
     */
    public static void removeExtra(String key) {
        SaTokenUserContextUtil.removeExtra(key);
    }

    /**
     * 续签Token
     * 委托给SaTokenUserContextUtil
     *
     * @param timeout 续签时间（秒）
     */
    public static void renewTimeout(long timeout) {
        SaTokenUserContextUtil.renewTimeout(timeout);
    }

    /**
     * 续签Token（Long参数重载）
     * 委托给SaTokenUserContextUtil
     *
     * @param timeout 续签时间（秒）
     */
    public static void renewTimeout(Long timeout) {
        if (timeout != null) {
            SaTokenUserContextUtil.renewTimeout(timeout.longValue());
        }
    }

    /**
     * 强制指定用户下线
     * 委托给SaTokenUserContextUtil
     *
     * @param userId 用户ID
     */
    public static void kickout(Long userId) {
        SaTokenUserContextUtil.kickout(userId);
    }


    /**
     * 用户上下文快照类
     * 委托给SaTokenUserContextUtil的内部类
     */
    public static class UserContextSnapshot {
        private final SaTokenUserContextUtil.UserContextSnapshot delegate;

        public UserContextSnapshot(SaTokenUserContextUtil.UserContextSnapshot delegate) {
            this.delegate = delegate;
        }

        public Long userId() {
            return delegate.userId();
        }

        public String username() {
            return delegate.username();
        }

        public String fullname() {
            return delegate.fullname();
        }

        public Long partnerId() {
            return delegate.partnerId();
        }

        public Long orgId() {
            return delegate.orgId();
        }

        @Override
        public String toString() {
            return delegate.toString();
        }

        @Override
        public boolean equals(Object obj) {
            return delegate.equals(obj);
        }

        @Override
        public int hashCode() {
            return delegate.hashCode();
        }
    }

    // ========== 请求上下文操作方法 ==========

    /**
     * 获取当前HTTP请求对象
     *
     * @return HttpServletRequest对象
     */
    public static HttpServletRequest getRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            LogUtils.warn(log, "获取HttpServletRequest失败", e);
            return null;
        }
    }

    /**
     * 获取当前HTTP响应对象
     *
     * @return HttpServletResponse对象
     */
    public static HttpServletResponse getResponse() {
        return responseLocal.get();
    }

    /**
     * 设置HTTP响应对象到ThreadLocal
     *
     * @param response HTTP响应对象
     */
    public static void setResponse(HttpServletResponse response) {
        responseLocal.remove();
        responseLocal.set(response);
    }

    /**
     * 清理HTTP响应对象ThreadLocal
     */
    public static void clearResponse() {
        responseLocal.remove();
    }

    /**
     * 获取当前HTTP会话对象
     *
     * @return HttpSession对象
     */
    public static HttpSession getSession() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getSession() : null;
    }


    /**
     * 获取请求参数值（字符串类型）
     *
     * @param name 参数名
     * @return 参数值，如果不存在则返回null
     */
    public static String getStringValue(String name) {
        HttpServletRequest request = getRequest();
        return request != null ? request.getParameter(name) : null;
    }

    /**
     * 获取请求参数值（Long类型）
     *
     * @param name 参数名
     * @return 参数值，如果不存在或转换失败则返回null
     */
    public static Long getLongValue(String name) {
        String value = getStringValue(name);
        if (StringUtils.isEmpty(value) || "undefined".equals(value)) {
            return null;
        }
        try {
            return Long.valueOf(value);
        } catch (NumberFormatException e) {
            LogUtils.warn(log, "参数转换为Long失败: name={}, value={}", name, value);
            return null;
        }
    }

    /**
     * 获取请求参数值（Integer类型）
     *
     * @param name 参数名
     * @return 参数值，如果不存在或转换失败则返回null
     */
    public static Integer getIntegerValue(String name) {
        String value = getStringValue(name);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            LogUtils.warn(log, "参数转换为Integer失败: name={}, value={}", name, value);
            return null;
        }
    }


    /**
     * 获取partnerId，如果请求参数中带有partnerId参数，则使用请求的partnerId，
     * 否则使用session中的partnerId，session中的partnerId由页面切换而来
     *
     * @return partnerId
     */
    public static Long getPartnerId() {
        Long partnerId = getLongValue(Constant.PartnerUser.Parameters.PARAM_KEY_PARTNER);
        if (partnerId == null) {
            try {
                HttpSession session = getSession();
                if (session != null && session.getAttribute(Constant.PartnerUser.Parameters.SESSION_KEY_PARTNER) != null) {
                    try {
                        partnerId = Long.parseLong(session.getAttribute(Constant.PartnerUser.Parameters.SESSION_KEY_PARTNER).toString());
                    } catch (NumberFormatException e) {
                        LogUtils.warn(log, "Session中的partnerId转换失败", e);
                    }
                } else {
                    partnerId = SaTokenUserContextUtil.getCurrentUserPartnerId();
                }
            } catch (org.springframework.data.redis.serializer.SerializationException e) {
                LogUtils.warn(log, "获取Session中的partnerId时发生序列化异常，使用Sa-Token获取: {}", e.getMessage());
                partnerId = SaTokenUserContextUtil.getCurrentUserPartnerId();
            } catch (Exception e) {
                LogUtils.warn(log, "获取Session中的partnerId时发生异常，使用Sa-Token获取", e);
                partnerId = SaTokenUserContextUtil.getCurrentUserPartnerId();
            }
        }
        return partnerId;
    }



}
